# OceanBase Docker Compose

这个项目提供了一个简单的Docker Compose配置来运行OceanBase数据库。

## 快速开始

### 1. 启动服务

```bash
docker-compose up -d
```

### 2. 检查服务状态

```bash
docker-compose ps
```

### 3. 查看日志

```bash
# 查看OceanBase日志
docker-compose logs -f oceanbase

# 查看所有服务日志
docker-compose logs -f
```

## 连接信息

### 直连OceanBase
- **主机**: localhost
- **端口**: 2881
- **用户名**: root@test (test租户) 或 root@sys (系统租户)
- **密码**: 无密码
- **租户**: test 或 sys

## 连接示例

### 使用MySQL客户端连接

```bash
# 连接到test租户
mysql -h 127.0.0.1 -P 2881 -u root@test

# 连接到sys租户（系统管理）
mysql -h 127.0.0.1 -P 2881 -u root@sys
```

### 使用容器内的obclient

```bash
# 进入OceanBase容器
docker exec -it oceanbase bash

# 在容器内连接test租户
obclient -h 127.0.0.1 -P 2881 -u root@test

# 在容器内连接sys租户
obclient -h 127.0.0.1 -P 2881 -u root@sys
```

### 使用MySQL客户端容器

```bash
# 启动MySQL客户端容器（如果已注释掉需要先取消注释）
docker run --rm -it --network docker-oceanbase_oceanbase_network mysql:8.0 mysql -h oceanbase -P 2881 -u root@test
```

## 数据持久化

- OceanBase数据存储在Docker volume `oceanbase_data` 中
- 日志文件映射到本地 `./logs` 目录

## 服务说明

### oceanbase
- OceanBase数据库主服务
- 使用mini模式，适合开发和测试
- 默认创建名为"test"的租户

### obproxy (可选)
- OceanBase代理服务
- 提供连接池和负载均衡功能
- 在生产环境中推荐使用

### mysql-client (可选)
- MySQL客户端容器
- 用于数据库管理和调试

## 管理命令

```bash
# 停止服务
docker-compose down

# 停止并删除数据
docker-compose down -v

# 重启服务
docker-compose restart

# 查看资源使用情况
docker-compose top
```

## 注意事项

1. 首次启动可能需要几分钟时间来初始化数据库
2. 确保端口2881、2882、2883没有被其他服务占用
3. 默认密码为123456，生产环境请修改为强密码
4. mini模式适合开发测试，生产环境建议使用集群模式

## 故障排除

如果遇到启动问题，可以：

1. 检查日志：`docker-compose logs oceanbase`
2. 检查端口占用：`netstat -tlnp | grep 2881`
3. 重新构建：`docker-compose down -v && docker-compose up -d`
