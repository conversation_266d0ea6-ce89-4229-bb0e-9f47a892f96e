version: '3.8'

services:
  oceanbase:
    image: oceanbase/oceanbase-ce:4.2.1.6
    container_name: oceanbase
    restart: unless-stopped
    ports:
      - "2881:2881"  # OceanBase SQL port
      - "2882:2882"  # OceanBase RPC port
    environment:
      - MODE=mini
      - OB_CLUSTER_NAME=obcluster
      - OB_TENANT_NAME=test
      - OB_USER_NAME=root
      - OB_USER_PASSWORD=123456
      - OB_ROOT_PASSWORD=123456
    volumes:
      - oceanbase_data:/root/ob
      - ./logs:/root/ob/log
    networks:
      - oceanbase_network
    healthcheck:
      test: ["CMD", "mysql", "-h", "127.0.0.1", "-P", "2881", "-u", "root", "-p123456", "-e", "SELECT 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 120s

  # OceanBase配置中心 (可选)
  obproxy:
    image: oceanbase/obproxy-ce:4.2.1
    container_name: obproxy
    restart: unless-stopped
    ports:
      - "2883:2883"  # OBProxy SQL port
    environment:
      - OB_CLUSTER=obcluster
      - OB_TENANT=test
      - PROXYRO_RS_LIST=oceanbase:2882
    depends_on:
      oceanbase:
        condition: service_healthy
    networks:
      - oceanbase_network

  # 可选：添加一个MySQL客户端容器用于管理
  mysql-client:
    image: mysql:8.0
    container_name: oceanbase-client
    restart: "no"
    command: sleep infinity
    networks:
      - oceanbase_network
    depends_on:
      oceanbase:
        condition: service_healthy

volumes:
  oceanbase_data:
    driver: local

networks:
  oceanbase_network:
    driver: bridge
