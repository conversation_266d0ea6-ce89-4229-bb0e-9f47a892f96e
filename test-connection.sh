#!/bin/bash

# OceanBase 连接测试脚本

echo "🔍 测试OceanBase连接..."

# 检查容器是否运行
if ! docker-compose ps | grep -q "oceanbase.*Up"; then
    echo "❌ OceanBase容器未运行，请先启动服务"
    echo "运行: ./start.sh 或 docker-compose up -d"
    exit 1
fi

echo "✅ OceanBase容器正在运行"

# 测试容器内连接
echo ""
echo "📊 测试容器内连接..."

echo "1. 测试sys租户连接："
docker exec oceanbase obclient -h 127.0.0.1 -P 2881 -u root@sys -e "SELECT 'sys租户连接成功' AS status;"

echo ""
echo "2. 测试test租户连接："
docker exec oceanbase obclient -h 127.0.0.1 -P 2881 -u root@test -e "SELECT 'test租户连接成功' AS status;"

echo ""
echo "3. 查看数据库列表："
docker exec oceanbase obclient -h 127.0.0.1 -P 2881 -u root@test -e "SHOW DATABASES;"

# 测试外部连接
echo ""
echo "📡 测试外部连接（使用MySQL客户端容器）..."
docker run --rm --network docker-oceanbase_oceanbase_network mysql:8.0 mysql -h oceanbase -P 2881 -u root@test -e "SELECT 'external connection successful' AS status;"

echo ""
echo "🎉 所有连接测试完成！"
echo ""
echo "💡 使用方法："
echo "  # 使用MySQL客户端连接（需要安装mysql客户端）"
echo "  mysql -h 127.0.0.1 -P 2881 -u root@test"
echo ""
echo "  # 使用Docker MySQL客户端连接"
echo "  docker run --rm -it --network docker-oceanbase_oceanbase_network mysql:8.0 mysql -h oceanbase -P 2881 -u root@test"
echo ""
echo "  # 进入OceanBase容器使用obclient"
echo "  docker exec -it oceanbase obclient -h 127.0.0.1 -P 2881 -u root@test"
