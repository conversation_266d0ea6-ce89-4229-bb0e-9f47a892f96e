#!/bin/bash

# OceanBase Docker Compose 启动脚本

set -e

echo "🚀 启动OceanBase Docker Compose..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose未安装"
    exit 1
fi

# 创建必要的目录
echo "📁 创建数据目录..."
mkdir -p logs

# 复制环境变量文件（如果不存在）
if [ ! -f .env ]; then
    echo "📋 创建环境变量文件..."
    cp .env.example .env
    echo "✅ 已创建.env文件，你可以根据需要修改配置"
fi

# 启动服务
echo "🔄 启动服务..."
docker-compose up -d

echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

echo ""
echo "🎉 OceanBase启动完成！"
echo ""
echo "连接信息："
echo "  连接test租户: mysql -h 127.0.0.1 -P 2881 -u root@test"
echo "  连接sys租户:  mysql -h 127.0.0.1 -P 2881 -u root@sys"
echo ""
echo "管理命令："
echo "  查看日志: docker-compose logs -f"
echo "  停止服务: docker-compose down"
echo "  重启服务: docker-compose restart"
echo ""
echo "等待数据库完全启动可能需要1-2分钟..."
